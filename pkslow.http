
# 双冥想小程序后端API测试文件
# 服务器地址: http://localhost:3004
# 注意：需要先执行登录接口获取token，然后在需要认证的接口中使用

### 变量定义
@baseUrl = http://localhost:3004
@token = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjQiLCJvcGVuaWQiOiJvcGVuaWRfdGVzdF8xMjMiLCJpYXQiOjE3NTQ4OTkwMDksImV4cCI6MTc1NzQ5MTAwOX0.w0pvzpB_BM4gJHjj9msNeMUWXXD7btYTzSjyTlDsxmQ
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU1NzU5Mjg1LCJleHAiOjE3NTU4NDU2ODV9.up_5g8nnxcSQ2bnAOnDS9ZkQng96fLT-yoyQ3jmW7HQ

### ========== 公共接口（无需认证） ==========

### 1. 测试公共GET接口
GET {{baseUrl}}/public/get

### 2. 公共API测试 - GET
GET {{baseUrl}}/public/api/test?param1=value1&param2=value2

### 3. 微信用户登录
POST {{baseUrl}}/api/public/user/login
Content-Type: application/json

{
  "openid": "openid_test_123",
  "nickname": "测试用户",
  "avatar_url": "https://youke1.picui.cn/s1/2025/08/11/68999a8cbe3d0.png",
  "unionid": "unionid_test_123"
}

### ========== 通用API接口 ==========

### 4. API测试 - POST
POST {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test",
  "value": "测试数据"
}

### 5. API测试 - PUT
PUT {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test",
  "value": "更新数据"
}

### 6. API测试 - DELETE
DELETE {{baseUrl}}/api/test
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "name": "test"
}

### ========== 认证相关接口 ==========

### 7. 检查认证
POST {{baseUrl}}/auth/check
Content-Type: application/json
Authorization: Bearer {{token}}

### ========== 用户相关接口 ==========

### 8. 获取用户基础信息
GET {{baseUrl}}/api/user/profile
Authorization: Bearer {{token}}

### 9. 获取用户收藏列表
GET {{baseUrl}}/api/user/favorites?pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### 10. 获取用户多肉列表
GET {{baseUrl}}/api/user/plants
Authorization: Bearer {{token}}

### 11. 获取用户冥想统计
GET {{baseUrl}}/api/user/meditation-stats
Authorization: Bearer {{token}}

### ========== 冥想内容相关接口 ==========

### 12. 获取冥想内容列表
GET {{baseUrl}}/api/meditation/list?pageNum=1&pageSize=10&type=meditation
Authorization: Bearer {{token}}

### 13. 获取冥想内容详情
GET {{baseUrl}}/api/meditation/1
Authorization: Bearer {{token}}

### 14. 收藏/取消收藏冥想内容
POST {{baseUrl}}/api/meditation/1/favorite
Content-Type: application/json
Authorization: Bearer {{token}}

### 15. 搜索冥想内容
GET {{baseUrl}}/api/meditation/search?keyword=冥想&type=冥想&pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### 16. 获取冥想标签列表
GET {{baseUrl}}/api/meditation/tags
Authorization: Bearer {{token}}

### ========== 计划相关接口 ==========

### 17. 获取每日计划
GET {{baseUrl}}/api/plan/daily?date=2025-08-11
Authorization: Bearer {{token}}

### 18. 添加到计划
POST {{baseUrl}}/api/plan/add
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "meditation_id": 1,
  "plan_date": "2025-08-11"
}

### 19. 从计划删除
DELETE {{baseUrl}}/api/plan/item/1
Authorization: Bearer {{token}}

### 20. 完成计划项
PUT {{baseUrl}}/api/plan/item/1/complete
Content-Type: application/json
Authorization: Bearer {{token}}

### 21. 获取历史计划
GET {{baseUrl}}/api/plan/history?pageNum=1&pageSize=10&start_date=2025-08-01&end_date=2025-08-11
Authorization: Bearer {{token}}

### 22. 获取计划统计
GET {{baseUrl}}/api/plan/stats?start_date=2025-08-01&end_date=2025-08-11
Authorization: Bearer {{token}}

### ========== 多肉相关接口 ==========

### 23. 获取多肉列表
GET {{baseUrl}}/api/plant/list
Authorization: Bearer {{token}}

### 24. 获取多肉详情
GET {{baseUrl}}/api/plant/1
Authorization: Bearer {{token}}

### 25. 为多肉增加能量
POST {{baseUrl}}/api/plant/1/energy
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "energy_value": 10,
  "reason": "完成冥想"
}

### 26. 创建新多肉
POST {{baseUrl}}/api/plant/create
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "species": "仙人掌"
}

### 27. 获取多肉成长记录
GET {{baseUrl}}/api/plant/1/records?pageNum=1&pageSize=10
Authorization: Bearer {{token}}

### ========== 文件上传接口 ==========

### 28. 文件上传 (需要实际文件)
POST {{baseUrl}}/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test.jpg"
Content-Type: image/jpeg

< ./test.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 29. 文件上传预检请求
OPTIONS {{baseUrl}}/upload

### ========== Swagger文档接口 ==========

### 30. 访问Swagger UI
GET {{baseUrl}}/swagger

### 31. 获取Swagger JSON
GET {{baseUrl}}/swagger.json

### ========== 测试用例说明 ==========

# 使用说明：
# 1. 首先执行第3个接口（微信用户登录）获取token
# 2. 将返回的token复制到文件顶部的@token变量中
# 3. 执行其他需要认证的接口时会自动使用该token
# 4. 如果token过期，需要重新登录获取新token

# 测试数据说明：
# - openid: 微信用户唯一标识，测试时可使用任意字符串
# - meditation_id: 冥想内容ID，需要先在数据库中存在对应记录
# - plan_date: 计划日期，格式为YYYY-MM-DD
# - plant_id: 多肉ID，通过获取多肉列表接口可以看到具体ID

# 分页参数说明：
# - pageNum: 当前页码，从1开始（支持向后兼容的page参数）
# - pageSize: 每页数量，默认10（支持向后兼容的limit参数）
# - 返回格式包含：total（总数量）、pageNum（当前页码）、pageSize（每页数量）、pages（总页数）、items（数据列表）

# 常见HTTP状态码：
# - 200: 请求成功
# - 400: 请求参数错误
# - 401: 未授权（token无效或过期）
# - 404: 资源不存在
# - 500: 服务器内部错误

### ========================================
### 后台管理系统接口测试
### ========================================

### ========== 管理员认证相关接口 ==========

### 32. 管理员登录
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 33. 获取管理员信息
GET {{baseUrl}}/admin/profile
Authorization: Bearer {{adminToken}}

### ========== 管理员管理（仅超级管理员） ==========

### 34. 获取管理员列表
GET {{baseUrl}}/admin/admins?page=1&limit=10&search=admin
Authorization: Bearer {{adminToken}}

### 35. 创建管理员
POST {{baseUrl}}/admin/admins
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "username": "double",
  "password": "double123",
  "real_name": "双双",
  "email": "<EMAIL>",
  "phone": "super_admin",
  "role": "editor"
}

### 36. 更新管理员信息
PUT {{baseUrl}}/admin/admins/2
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "real_name": "双双1",
  "email": "<EMAIL>",
  "role": "admin",
  "status": "active"
}

### 36.1 删除管理员
DELETE {{baseUrl}}/admin/admins/2
Authorization: Bearer {{adminToken}}


### ========== 用户管理模块 ==========

### 37. 获取用户列表
# GET {{baseUrl}}/admin/users?page=1&limit=10&search=欢欢&meditation_level=2&start_date=2025-08-01&end_date=2025-08-18
GET {{baseUrl}}/admin/users?page=1&limit=10&meditation_level=2&start_date=2025-08-01&end_date=2025-08-18
Authorization: Bearer {{adminToken}}


### 38. 获取用户详情
GET {{baseUrl}}/admin/users/1
Authorization: Bearer {{adminToken}}

### 39. 更新用户等级
PUT {{baseUrl}}/admin/users/1/level
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "meditation_level": 3,
  "reason": "手动调整用户等级测试"
}

### 40. 获取用户统计数据
GET {{baseUrl}}/admin/users/statistics
Authorization: Bearer {{adminToken}}

### ========== 用户等级管理 ==========

### 41. 获取等级配置列表
GET {{baseUrl}}/admin/user-levels
Authorization: Bearer {{adminToken}}

### 42. 创建等级配置
POST {{baseUrl}}/admin/user-levels
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "level": 6,
  "level_name": "宗师",
  "required_days": 60,
  "required_duration": 108000,
  "benefits": {
    "description": "坚持冥想60天",
    "features": ["解锁传说多肉", "能量获取+100%", "专属头像框"]
  },
  "icon_url": "https://example.com/level6.png"
}

### 43. 更新等级配置
PUT {{baseUrl}}/admin/user-levels/6
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "level_name": "冥想宗师",
  "required_days": 90,
  "benefits": {
    "description": "坚持冥想90天",
    "features": ["解锁所有传说多肉", "能量获取+150%", "专属称号", "专属头像框"]
  }
}

### 44. 删除等级配置
DELETE {{baseUrl}}/admin/user-levels/6
Authorization: Bearer {{adminToken}}

### ========== 冥想内容管理 ==========

### 45. 获取冥想内容列表（管理端）
GET {{baseUrl}}/admin/meditation/contents?page=1&limit=10&search=正念&type=meditation&sub_type=course&status=published
Authorization: Bearer {{adminToken}}

### 46. 创建冥想内容
POST {{baseUrl}}/admin/meditation/contents
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "title": "深度正念呼吸冥想",
  "description": "通过专注呼吸来培养深度正念，适合有一定基础的练习者",
  "type": "meditation",
  "sub_type": "single",
  "parent_id": null,
  "cover_url": "https://example.com/covers/deep_breathing.jpg",
  "audio_url": "https://example.com/audios/deep_breathing.mp3",
  "video_url": "https://example.com/videos/deep_breathing.mp4",
  "duration": 1800,
  "tag_ids": [1, 2, 3],
  "status": "published"
}

### 47. 获取冥想内容详情（管理端）
GET {{baseUrl}}/admin/meditation/contents/1
Authorization: Bearer {{adminToken}}

### 48. 更新冥想内容
PUT {{baseUrl}}/admin/meditation/contents/1
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "title": "高级正念呼吸冥想",
  "description": "更新后的描述：深度正念呼吸练习，帮助达到更深层的冥想状态",
  "duration": 2400,
  "tag_ids": [1, 2, 4],
  "status": "published"
}

### 49. 删除冥想内容
DELETE {{baseUrl}}/admin/meditation/contents/10
Authorization: Bearer {{adminToken}}

### ========== 标签管理 ==========

### 50. 获取标签列表（管理端）
GET {{baseUrl}}/admin/meditation/tags?page=1&limit=10&search=正念
Authorization: Bearer {{adminToken}}

### 51. 创建标签
POST {{baseUrl}}/admin/meditation/tags
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "深度放松"
}

### 52. 更新标签
PUT {{baseUrl}}/admin/meditation/tags/5
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "极致放松"
}

### 53. 删除标签
DELETE {{baseUrl}}/admin/meditation/tags/5
Authorization: Bearer {{adminToken}}

### 54. 批量删除标签
DELETE {{baseUrl}}/admin/meditation/tags/batch-delete
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "tag_ids": [6, 7, 8]
}

### ========== 多肉品种管理 ==========

### 55. 获取多肉品种列表
GET {{baseUrl}}/admin/plants/species?page=1&limit=10&search=多肉&rarity=rare
Authorization: Bearer {{adminToken}}

### 56. 创建多肉品种
POST {{baseUrl}}/admin/plants/species
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "rainbow_succulent",
  "display_name": "彩虹多肉",
  "description": "拥有彩虹般色彩的神奇多肉，极其稀有珍贵",
  "rarity": "legendary",
  "unlock_condition": {
    "level": 5,
    "days": 30,
    "special_achievement": "rainbow_master"
  },
  "growth_stages": {
    "stages": [
      {"level": 1, "name": "彩虹种子"},
      {"level": 15, "name": "彩虹幼苗"},
      {"level": 30, "name": "彩虹之花"}
    ]
  },
  "max_level": 30,
  "base_energy_per_level": 200,
  "image_urls": {
    "1": "/images/plants/rainbow_1.png",
    "15": "/images/plants/rainbow_15.png",
    "30": "/images/plants/rainbow_30.png"
  }
}

### 57. 更新多肉品种
PUT {{baseUrl}}/admin/plants/species/1
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "display_name": "超级彩虹多肉",
  "description": "更新后的描述：拥有七彩光芒的传说级多肉",
  "max_level": 50,
  "base_energy_per_level": 250,
  "is_active": true
}

### 58. 删除多肉品种
DELETE {{baseUrl}}/admin/plants/species/3
Authorization: Bearer {{adminToken}}

### ========== 能量奖励管理 ==========

### 59. 获取能量奖励规则列表
GET {{baseUrl}}/admin/plants/energy-rules
Authorization: Bearer {{adminToken}}

### 60. 创建能量奖励规则
POST {{baseUrl}}/admin/plants/energy-rules
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "rule_name": "完成长时间冥想",
  "rule_type": "meditation_complete",
  "condition": {
    "min_duration": 3600,
    "meditation_type": "meditation"
  },
  "energy_amount": 100,
  "bonus_multiplier": 1.5,
  "max_daily_times": 1,
  "description": "完成1小时冥想获得额外奖励",
  "priority": 10
}

### 61. 创建连续天数奖励规则
POST {{baseUrl}}/admin/plants/energy-rules
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "rule_name": "连续冥想21天",
  "rule_type": "daily_streak",
  "condition": {
    "streak_days": 21
  },
  "energy_amount": 500,
  "bonus_multiplier": 2.0,
  "max_daily_times": 1,
  "description": "连续冥想21天获得大量能量奖励",
  "priority": 8
}

### 62. 创建等级提升奖励规则
POST {{baseUrl}}/admin/plants/energy-rules
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "rule_name": "用户等级提升",
  "rule_type": "level_up",
  "condition": {},
  "energy_amount": 300,
  "bonus_multiplier": 1.0,
  "max_daily_times": 0,
  "description": "用户冥想等级提升时获得奖励",
  "priority": 5
}

### ========== 统计数据 ==========

### 63. 获取多肉统计数据
GET {{baseUrl}}/admin/plants/statistics
Authorization: Bearer {{adminToken}}

### ========== 管理端文件上传 ==========

### 64. 管理端文件上传
POST {{baseUrl}}/admin/upload
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
Authorization: Bearer {{adminToken}}

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="admin_test.jpg"
Content-Type: image/jpeg

< ./test.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### ========== 管理端测试用例说明 ==========

# 后台管理系统使用说明：
# 1. 首先执行第32个接口（管理员登录）获取adminToken
# 2. 将返回的token复制到文件顶部的@adminToken变量中
# 3. 执行其他管理端接口时会自动使用该adminToken
# 4. 管理员token有效期为24小时

# 权限说明：
# - super_admin: 拥有所有权限，包括管理员管理
# - admin: 拥有大部分管理权限，不能管理其他管理员
# - editor: 只能管理内容相关功能（内容、标签）

# 默认管理员账户：
# - 用户名: admin
# - 密码: admin123
# - 角色: super_admin
# 注意：首次登录后请立即修改密码！

# 测试数据说明：
# - 用户ID: 通过获取用户列表接口可以看到具体ID
# - 内容ID: 通过获取内容列表接口可以看到具体ID
# - 标签ID: 通过获取标签列表接口可以看到具体ID
# - 品种ID: 通过获取品种列表接口可以看到具体ID

# 分页参数说明（管理端）：
# - page: 当前页码，从1开始
# - limit: 每页数量，默认10
# - search: 搜索关键词（可选）
# - 其他筛选参数根据具体接口而定

# 管理端常见HTTP状态码：
# - 200: 请求成功
# - 400: 请求参数错误
# - 401: 未认证或认证失败
# - 403: 权限不足
# - 404: 资源不存在
# - 500: 服务器内部错误

# 安全提醒：
# - 管理端接口具有较高权限，请谨慎操作
# - 删除操作不可逆，请确认后再执行
# - 定期更换管理员密码
# - 生产环境中请使用HTTPS

### ========== 错误场景测试 ==========

### 65. 管理员登录 - 错误密码
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "wrong_password"
}

### 66. 管理员登录 - 缺少参数
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin"
}

### 67. 无权限访问管理员列表（使用普通用户token）
GET {{baseUrl}}/admin/admins
Authorization: Bearer {{token}}

### 68. 创建重复用户名的管理员
POST {{baseUrl}}/admin/admins
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "username": "admin",
  "password": "123456",
  "real_name": "重复管理员",
  "role": "admin"
}

### 69. 创建标签 - 重复名称
POST {{baseUrl}}/admin/meditation/tags
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "name": "正念"
}

### 70. 获取不存在的用户详情
GET {{baseUrl}}/admin/users/99999
Authorization: Bearer {{adminToken}}

### 71. 删除正在使用的标签
DELETE {{baseUrl}}/admin/meditation/tags/1
Authorization: Bearer {{adminToken}}

### 72. 更新不存在的等级配置
PUT {{baseUrl}}/admin/user-levels/99999
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "level_name": "不存在的等级"
}

### ========== 边界情况测试 ==========

### 73. 获取用户列表 - 大页码
GET {{baseUrl}}/admin/users?page=999&limit=10
Authorization: Bearer {{adminToken}}

### 74. 获取用户列表 - 超大每页数量
GET {{baseUrl}}/admin/users?page=1&limit=1000
Authorization: Bearer {{adminToken}}

### 75. 创建等级配置 - 最小值
POST {{baseUrl}}/admin/user-levels
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "level": 0,
  "level_name": "测试等级",
  "required_days": 0,
  "required_duration": 0
}

### 76. 搜索 - 空关键词
GET {{baseUrl}}/admin/users?search=
Authorization: Bearer {{adminToken}}

### 77. 搜索 - 特殊字符
GET {{baseUrl}}/admin/meditation/contents?search=%20!@#$%^&*()
Authorization: Bearer {{adminToken}}

### ========== 综合业务流程测试 ==========

### 78. 完整的内容管理流程 - 创建草稿
POST {{baseUrl}}/admin/meditation/contents
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "title": "流程测试冥想",
  "description": "用于测试完整流程的冥想内容",
  "type": "meditation",
  "sub_type": "single",
  "duration": 900,
  "status": "draft"
}

### 79. 完整的内容管理流程 - 发布内容
PUT {{baseUrl}}/admin/meditation/contents/2
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "status": "published"
}

### 80. 完整的用户管理流程 - 调整用户等级
PUT {{baseUrl}}/admin/users/1/level
Content-Type: application/json
Authorization: Bearer {{adminToken}}

{
  "meditation_level": 4,
  "reason": "完整流程测试 - 用户表现优秀"
}

### ========== 最终测试说明 ==========

# 测试执行建议：
# 1. 按顺序执行基础功能测试（32-63）
# 2. 执行错误场景测试（64-71）验证错误处理
# 3. 执行边界情况测试（72-76）验证系统稳定性
# 4. 执行综合流程测试（77-79）验证端到端功能

# 测试数据清理：
# 测试完成后，建议清理测试数据：
# - 删除测试创建的管理员账户
# - 删除测试创建的标签和内容
# - 恢复测试修改的用户等级
# - 删除测试创建的多肉品种

# 自动化测试建议：
# 可以将这些测试用例转换为自动化测试脚本
# 建议使用 Newman (Postman CLI) 或类似工具进行批量测试