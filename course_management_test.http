### 课程管理模块接口测试
### 基础URL配置
@baseUrl = http://localhost:3004
@adminToken = YOUR_ADMIN_TOKEN_HERE

### 1. 管理员登录获取Token
POST {{baseUrl}}/admin/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

### 2. 获取冥想内容列表（包含课程）
GET {{baseUrl}}/admin/meditation/contents?type=meditation&sub_type=course
Authorization: Bearer {{adminToken}}

### 3. 创建新课程
POST {{baseUrl}}/admin/meditation/contents
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "高级正念冥想课程",
  "description": "适合有一定基础的冥想者，深入学习正念冥想技巧",
  "type": "meditation",
  "sub_type": "course",
  "cover_url": "https://example.com/covers/advanced_mindfulness.jpg",
  "duration": 0,
  "tag_ids": [1, 2],
  "status": "draft"
}

### 4. 推荐课程
PUT {{baseUrl}}/admin/meditation/contents/1/recommend
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "is_recommended": true
}

### 5. 取消推荐课程
PUT {{baseUrl}}/admin/meditation/contents/1/recommend
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "is_recommended": false
}

### 6. 上架课程（发布）
PUT {{baseUrl}}/admin/meditation/contents/1/status
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "status": "published"
}

### 7. 下架课程（归档）
PUT {{baseUrl}}/admin/meditation/contents/1/status
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "status": "archived"
}

### 8. 获取课程章节列表
GET {{baseUrl}}/admin/meditation/courses/1/chapters
Authorization: Bearer {{adminToken}}

### 9. 为课程添加章节
POST {{baseUrl}}/admin/meditation/courses/1/chapters
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "高级正念冥想第一课：深度呼吸练习",
  "description": "学习更深层次的呼吸冥想技巧",
  "cover_url": "https://example.com/covers/advanced_breath.jpg",
  "audio_url": "https://example.com/audio/advanced_breath.mp3",
  "duration": 1200,
  "tag_ids": [1, 2],
  "sort_order": 1
}

### 10. 为课程添加第二个章节
POST {{baseUrl}}/admin/meditation/courses/1/chapters
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "title": "高级正念冥想第二课：情绪深度观察",
  "description": "深入探索情绪的本质和观察技巧",
  "cover_url": "https://example.com/covers/advanced_emotion.jpg",
  "audio_url": "https://example.com/audio/advanced_emotion.mp3",
  "duration": 1500,
  "tag_ids": [1, 3],
  "sort_order": 2
}

### 11. 调整章节顺序
PUT {{baseUrl}}/admin/meditation/courses/1/chapters/reorder
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "chapter_orders": [
    {
      "id": 2,
      "sort_order": 1
    },
    {
      "id": 3,
      "sort_order": 2
    }
  ]
}

### 12. 删除课程章节
DELETE {{baseUrl}}/admin/meditation/courses/1/chapters/3
Authorization: Bearer {{adminToken}}

### 13. 获取推荐课程列表
GET {{baseUrl}}/admin/meditation/contents?is_recommended=true
Authorization: Bearer {{adminToken}}

### 14. 获取已发布课程列表
GET {{baseUrl}}/admin/meditation/contents?status=published&sub_type=course
Authorization: Bearer {{adminToken}}

### 15. 删除课程（需要先删除所有章节）
DELETE {{baseUrl}}/admin/meditation/contents/1
Authorization: Bearer {{adminToken}}
