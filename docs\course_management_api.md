# 课程管理模块 API 文档

## 概述

课程管理模块为冥想应用提供了完整的课程内容管理功能，包括课程的创建、编辑、推荐、上下架以及章节管理等功能。

## 功能特性

### 1. 课程基础管理
- ✅ 创建课程
- ✅ 编辑课程信息
- ✅ 删除课程
- ✅ 获取课程列表和详情

### 2. 课程状态管理
- ✅ 推荐/取消推荐课程
- ✅ 上架/下架课程（发布状态管理）
- ✅ 归档课程

### 3. 章节管理
- ✅ 获取课程章节列表
- ✅ 添加章节到课程
- ✅ 删除课程章节
- ✅ 调整章节顺序

## 数据库变更

### 新增字段

在 `meditation_content` 表中新增了以下字段：

```sql
-- 推荐状态字段
is_recommended BOOLEAN DEFAULT FALSE COMMENT '是否推荐'

-- 排序字段
sort_order INT DEFAULT 0 COMMENT '排序顺序'
```

### 新增索引

```sql
-- 推荐状态索引
CREATE INDEX idx_meditation_content_recommended ON meditation_content(is_recommended);

-- 排序字段索引
CREATE INDEX idx_meditation_content_sort_order ON meditation_content(sort_order);

-- 复合索引
CREATE INDEX idx_meditation_content_status_rec_sort ON meditation_content(status, is_recommended, sort_order);
CREATE INDEX idx_meditation_content_type_status ON meditation_content(type, sub_type, status);
```

## API 接口

### 课程状态管理

#### 1. 推荐/取消推荐课程
```http
PUT /admin/meditation/contents/{id}/recommend
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "is_recommended": true
}
```

#### 2. 修改课程状态（上架/下架）
```http
PUT /admin/meditation/contents/{id}/status
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "status": "published"  // draft, published, archived
}
```

### 章节管理

#### 3. 获取课程章节列表
```http
GET /admin/meditation/courses/{courseId}/chapters
Authorization: Bearer {admin_token}
```

#### 4. 添加章节到课程
```http
POST /admin/meditation/courses/{courseId}/chapters
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "title": "章节标题",
  "description": "章节描述",
  "cover_url": "封面图片URL",
  "audio_url": "音频文件URL",
  "video_url": "视频文件URL",
  "duration": 1200,
  "tag_ids": [1, 2],
  "sort_order": 1
}
```

#### 5. 删除课程章节
```http
DELETE /admin/meditation/courses/{courseId}/chapters/{chapterId}
Authorization: Bearer {admin_token}
```

#### 6. 调整章节顺序
```http
PUT /admin/meditation/courses/{courseId}/chapters/reorder
Authorization: Bearer {admin_token}
Content-Type: application/json

{
  "chapter_orders": [
    {
      "id": 2,
      "sort_order": 1
    },
    {
      "id": 3,
      "sort_order": 2
    }
  ]
}
```

## 权限控制

### 角色权限说明

- **super_admin（超级管理员）**: 拥有所有权限
- **admin（管理员）**: 拥有大部分管理权限
- **editor（编辑员）**: 拥有内容编辑权限

### 接口权限分配

| 功能 | super_admin | admin | editor |
|------|-------------|-------|--------|
| 推荐/取消推荐 | ✅ | ✅ | ❌ |
| 修改状态 | ✅ | ✅ | ✅ |
| 添加章节 | ✅ | ✅ | ✅ |
| 删除章节 | ✅ | ✅ | ❌ |
| 调整章节顺序 | ✅ | ✅ | ✅ |
| 获取章节列表 | ✅ | ✅ | ✅ |

## 使用示例

### 完整的课程创建流程

1. **创建课程**
```http
POST /admin/meditation/contents
{
  "title": "正念冥想入门",
  "type": "meditation",
  "sub_type": "course",
  "status": "draft"
}
```

2. **添加章节**
```http
POST /admin/meditation/courses/1/chapters
{
  "title": "第一课：观察呼吸",
  "duration": 600,
  "sort_order": 1
}
```

3. **发布课程**
```http
PUT /admin/meditation/contents/1/status
{
  "status": "published"
}
```

4. **设为推荐**
```http
PUT /admin/meditation/contents/1/recommend
{
  "is_recommended": true
}
```

## 错误处理

### 常见错误码

- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式

```json
{
  "code": 400,
  "message": "错误描述",
  "error": "详细错误信息"
}
```

## 测试

使用提供的 `course_management_test.http` 文件进行接口测试。

## 注意事项

1. 删除课程前需要先删除所有章节
2. 章节的 `sort_order` 用于控制显示顺序
3. 推荐状态影响前端的推荐列表显示
4. 状态为 `archived` 的内容不会在前端显示
